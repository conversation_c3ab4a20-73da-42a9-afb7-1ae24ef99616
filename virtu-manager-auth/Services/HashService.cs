using virtu_manager_auth.Services.Interfaces;

namespace virtu_manager_auth.Services;

public class HashService : IHashService
{
    private readonly int _workFactor;
    
    public HashService(int workFactor)
    {
        ArgumentNullException.ThrowIfNull(workFactor);
        
        if (workFactor <=  0) throw new ArgumentException("Work factor must be greater than 0");

        _workFactor = workFactor;
    }
    
    public string HashData(string data)
    {
        ArgumentNullException.ThrowIfNull(data);
        
        return BCrypt.Net.BCrypt.HashPassword(data, _workFactor);
    }

    public bool VerifyData(string data, string hash)
    {
        ArgumentNullException.ThrowIfNull(data);
        ArgumentNullException.ThrowIfNull(hash);
        
        return BCrypt.Net.BCrypt.Verify(data, hash);
    }
}