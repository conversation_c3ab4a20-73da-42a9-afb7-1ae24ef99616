using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using virtu_manager_auth.Core.Exceptions;

namespace virtu_manager_auth.Filters;

public class ExceptionFilters : IExceptionFilter
{
    private readonly ILogger<ExceptionFilters> _logger;
    
    public ExceptionFilters(ILogger<ExceptionFilters> logger)
    {
        ArgumentNullException.ThrowIfNull(logger, nameof(logger));
        _logger = logger;
    }

    public void OnException(ExceptionContext context)
    {
        int statusCode;
        object response;

        switch (context.Exception)
        {
            case NotFoundException exception:
                statusCode = StatusCodes.Status404NotFound;
                response = exception.Message;
                _logger.LogError(exception.Message, exception);
                break;
            case ConflictException exception:
                statusCode = StatusCodes.Status409Conflict;
                response = exception.Message;
                _logger.LogError(exception.Message, exception);
                break;
            case BadRequestException exception:
                statusCode = StatusCodes.Status400BadRequest;
                response = exception.Message;
                _logger.LogError(exception.Message,  exception);
                break;
            case UnauthorizedException exception:
                statusCode = StatusCodes.Status401Unauthorized;
                response = exception.Message;
                _logger.LogError(exception.Message,  exception);
                break;
            case ForbiddenException exception:
                statusCode = StatusCodes.Status403Forbidden;
                response = exception.Message;
                _logger.LogError(exception.Message,  exception);
                break;
            default:
                statusCode = StatusCodes.Status500InternalServerError;
                response = new { Message = "Internal Server Error"};
                _logger.LogError(context.Exception.Message,  context.Exception);
                break;
        }

        context.Result = new ObjectResult(response)
        {
            StatusCode = statusCode
        };
        
        context.ExceptionHandled = true;
    }
}