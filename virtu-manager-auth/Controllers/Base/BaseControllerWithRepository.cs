using Microsoft.AspNetCore.Mvc;
using virtu_manager_auth.Repositories;

namespace virtu_manager_auth.Controllers.Base;

public class BaseControllerWithRepository<TRepository, TRepositoryType> : ControllerBase
    where TRepository : AbstractRepository<TRepositoryType>
{
    protected readonly TRepository Repository;

    public BaseControllerWithRepository(TRepository repository)
    {
        ArgumentNullException.ThrowIfNull(repository);
        Repository = repository;
    }
}