using System.ComponentModel.DataAnnotations;
using virtu_manager_auth.Database.Tables.UserTable;

namespace virtu_manager_auth.Api.ViewModels;

public class UserViewModel
{
    public Guid Id { get; set; }
    
    public string Login { get; set; } = string.Empty;
    
    public string Email { get; set; } = string.Empty;
    
    [EnumDataType(typeof(UserRole))]
    public UserRole Role { get; set; }
    
    public DateTime CreatedAt { get; set; }
    
    public DateTime? UpdatedAt { get; set; }
    
    public DateTime? LastLoginAt { get; set; }
}