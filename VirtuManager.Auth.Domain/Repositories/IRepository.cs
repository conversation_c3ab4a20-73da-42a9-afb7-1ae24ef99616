using VirtuManager.Auth.Domain.Entities.Base;
using VirtuManager.Auth.Domain.Pagination;

namespace VirtuManager.Auth.Domain.Repositories;

public interface IRepository<TEntity, in TKey> where TEntity : class, IEntity<TKey> where TK<PERSON> : IEquatable<TKey>
{
    Task AddAsync(TEntity entity, CancellationToken cancellationToken = default);
    Task RemoveAsync(TEntity entity, CancellationToken cancellationToken = default);
    Task UpdateRange(IEnumerable<TEntity> entities);
    Task UpdateAsync(TEntity entity, CancellationToken cancellationToken = default);
    

    Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default);
    Task<List<TEntity>?> GetAllAsync(CancellationToken cancellationToken = default);
    Task<PagedResult<TEntity>> GetPagedAsync(Pageable pageable, CancellationToken cancellationToken = default);
}