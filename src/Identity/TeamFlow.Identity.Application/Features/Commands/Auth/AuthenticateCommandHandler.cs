using System.Linq.Expressions;
using System.Net;
using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Dto.Client;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Auth;
using TeamFlow.Identity.Application.Contracts.Models;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Services.Interfaces;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Enums.Jwt;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Shared.Repositories.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Auth;

public sealed class AuthenticateCommandHandler : IRequestHandler<AuthenticateCommand, Result<JwtTokens>>
{
    private readonly IGenericRepository<User, Guid> _usersRepository;
    private readonly IGenericRepository<AuthEvent, Guid> _authEventsRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IHashDataService _hashDataService;
    private readonly IAuthContextProvider _authContextProvider;
    private readonly IJwtService _jwtService;
    private readonly IValidator<AuthenticateCommand> _validator;
    private readonly ILogger<AuthenticateCommandHandler> _logger;

    public AuthenticateCommandHandler(
        IGenericRepository<User, Guid> usersRepository,
        IHashDataService hashDataService,
        IJwtService jwtService,
        IValidator<AuthenticateCommand> validator,
        IAuthContextProvider authContextProvider,
        IGenericRepository<AuthEvent, Guid> authEventsRepository, 
        ILogger<AuthenticateCommandHandler> logger, 
        IUnitOfWork unitOfWork)
    {
        _usersRepository = usersRepository;
        _hashDataService = hashDataService;
        _jwtService = jwtService;
        _validator = validator;
        _authContextProvider = authContextProvider;
        _authEventsRepository = authEventsRepository;
        _logger = logger;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<JwtTokens>> Handle(AuthenticateCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(User), validationResult));
        }

        Expression<Func<User, bool>> predicate = u =>
            u.Login == request.Login
            || u.Email == request.Email;

        var user = await _usersRepository.GetOneAsync(predicate, cancellationToken);

        if (user is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(User), (request.Login ?? request.Email)!));
        }

        var isValidPassword = _hashDataService.VerifyData(request.Password, user.PasswordHash);

        if (!isValidPassword)
        {
            return Result.Fail(ErrorsFactory.Custom("Invalid Password", HttpStatusCode.Unauthorized));
        }

        var payload = new TokensPayload(user.Id, user.Login, user.Role, user.PositionId);

        var tokens = new JwtTokens(
            _jwtService.GenerateAccessToken(payload),
            _jwtService.GenerateRefreshToken(payload));

        var hashedRefreshToken = _hashDataService.HashData(tokens.RefreshToken);
        await using var transaction = await _unitOfWork.BeginTransactionAsync(cancellationToken);

        try
        {
            var token = new Token
            {
                Id = Guid.CreateVersion7(),
                UserId = user.Id,
                HashedToken = hashedRefreshToken,
                ExpiresAt = DateTime.UtcNow.AddDays(14),
                CreatedAt = DateTime.UtcNow
            };

            await CreateAuthEventAsync(user.Id, AuthEventType.Login, request.DeviceInfo, cancellationToken);

            user.Tokens.Add(token);
            user.UpdatedAt = DateTime.UtcNow;

            await _usersRepository.UpdateAsync(user, cancellationToken);
            await transaction.CommitAsync(cancellationToken);
            return Result.Ok(tokens);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error authenticating user {UserId}", user.Id);
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }

    private async Task CreateAuthEventAsync(Guid userId, AuthEventType eventType, ClientDeviceInfo? clientDeviceInfo,
        CancellationToken cancellationToken)
    {
        try
        {
            var authContext = await _authContextProvider.GetAuthContextAsync();
            var finalDeviceInfo = MergeDeviceInfo(clientDeviceInfo, ParseUserAgent(authContext.UserAgent));

            var authEvent = new AuthEvent
            {
                Id = Guid.CreateVersion7(),
                UserId = userId,
                EventType = eventType,
                IpAddress = authContext.IpAddress,
                Country = authContext.Country,
                City = authContext.City,
                DeviceType = finalDeviceInfo.DeviceType,
                DeviceName = finalDeviceInfo.DeviceName,
                OsName = finalDeviceInfo.OsName,
                OsVersion = finalDeviceInfo.OsVersion,
                BrowserName = finalDeviceInfo.BrowserName,
                BrowserVersion = finalDeviceInfo.BrowserVersion,
                LastActivity = authContext.RequestTime,
                IsActive = eventType == AuthEventType.Login,
                CreatedAt = authContext.RequestTime
            };

            await _authEventsRepository.AddAsync(authEvent, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating auth event");
            throw;
        }
    }

    private static ClientDeviceInfo MergeDeviceInfo(ClientDeviceInfo? clientInfo, ClientDeviceInfo serverInfo)
    {
        if (clientInfo == null) return serverInfo;

        return new ClientDeviceInfo
        {
            DeviceType = clientInfo.DeviceType ?? serverInfo.DeviceType,
            DeviceName = clientInfo.DeviceName ?? serverInfo.DeviceName,
            OsName = clientInfo.OsName ?? serverInfo.OsName,
            OsVersion = clientInfo.OsVersion ?? serverInfo.OsVersion,
            BrowserName = clientInfo.BrowserName ?? serverInfo.BrowserName,
            BrowserVersion = clientInfo.BrowserVersion ?? serverInfo.BrowserVersion
        };
    }
}