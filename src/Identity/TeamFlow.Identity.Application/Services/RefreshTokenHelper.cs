using FluentResults;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Services.Interfaces;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Errors;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Extensions;

namespace TeamFlow.Identity.Application.Services;

public class RefreshTokenHelper : IRefreshTokenHelper
{
    private readonly IJwtService _jwtService;
    private readonly IHashDataService _hashDataService;
    private readonly IGenericRepository<User, Guid> _usersRepository;

    public RefreshTokenHelper(
        IJwtService jwtService,
        IHashDataService hashDataService,
        IGenericRepository<User, Guid> usersRepository)
    {
        _jwtService = jwtService;
        _hashDataService = hashDataService;
        _usersRepository = usersRepository;
    }
    
    public async Task<Result<User>> GetUserFromRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(refreshToken))
        {
            return Result.Fail(AuthErrors.InvalidRefreshToken);
        }

        var principal = _jwtService.ValidateToken(refreshToken);
        if (principal is null)
        {
            return Result.Fail(AuthErrors.InvalidRefreshToken);
        }

        var tokenType = principal.GetTokenType();
        if (tokenType is not nameof(JwtClaimValue.Refresh))
        {
            return Result.Fail(ErrorsFactory.Unauthorized(AuthErrors.InvalidRefreshToken));
        }

        var userId = principal.GetUserId();
        
        var user = await _usersRepository.GetOneAsync(x => x.Id == userId, cancellationToken);
        if (user is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(User), userId));
        }
        
        var tokenHash = user.Sessions.FirstOrDefault(x => x.HashedToken == refreshToken)?.HashedToken;

        var isValidRefreshToken = _hashDataService.VerifyData(refreshToken, tokenHash!);
        return !isValidRefreshToken 
            ? Result.Fail(ErrorsFactory.Unauthorized(AuthErrors.InvalidRefreshToken)) 
            : Result.Ok(user);
    }

    public Task<Result<UserSession>> GetUserSessionByTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}