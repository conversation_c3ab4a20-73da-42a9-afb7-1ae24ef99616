using Microsoft.EntityFrameworkCore;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Enums.Settings;
using TeamFlow.Identity.Persistence.Database.Config;
using TeamFlow.Shared.Repositories.Entities;
using UserRole = TeamFlow.Shared.Contracts.Enums.UserRole;

namespace TeamFlow.Identity.Persistence.Database;

/*
 * TODO [PERSISTENCE]
 * Или использовать User
 * Или продублировать без наследования в UserEntity. И делать мапинг
 * Пока выбрал юзать напрямую модели без мапинга, мапниг нужен только для [LogEntry]
 */
public class ApplicationDbContext : DbContext
{
    public DbSet<User> Users { get; private set; }

    public DbSet<Position> Positions { get; private set; }

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new UsersConfiguration());
        modelBuilder.ApplyConfiguration(new PositionsConfiguration());
        modelBuilder.ApplyConfiguration(new UserSettingsConfiguration());
        modelBuilder.ApplyConfiguration(new SkillsConfiguration());
        modelBuilder.ApplyConfiguration(new UserSkillConfiguration());
        
        var systemAdminGuid = new Guid("11111111-1111-1111-1111-111111111111");
        var userAdminGuid = new Guid("*************-2222-2222-************");

        modelBuilder.Entity<Position>().HasData(new Position
        {
            Id = systemAdminGuid,
            Name = "System Administrator",
            CreatedAt = new DateTime(2025, 2, 21, 0, 0, 0, DateTimeKind.Utc),
        });

        /*
         * The Original Password is admin12345
         * Hashed with BCrypt work factor 9
         */
        const string adminPasswordHash = "$2a$09$jIqGzmvhNyBGmqJgs.qcAuuntFF8jmK1sacrwbmvmLm4jwv0GITuC";

        modelBuilder.Entity<User>().HasData(new User
        {
            Id = userAdminGuid,
            Login = "admin",
            PasswordHash = adminPasswordHash,
            Role = UserRole.Admin,
            Email = "<EMAIL>",
            CreatedAt = new DateTime(2025, 2, 21, 0, 0, 0, DateTimeKind.Utc),
            PositionId = systemAdminGuid
        });

        modelBuilder.Entity<UserSettings>().HasData(new UserSettings
        {
            Id = new Guid("0197455d-3d19-7eae-8924-2db4967e25d0"),
            EmailNotification = false,
            SystemNotification = false,
            EventReminder = false,
            PanelMode = SidebarPanelMode.Extended,
            UiTheme = UiTheme.System,
            Language = Language.Ru,
            CreatedAt = new DateTime(2025, 2, 21, 0, 0, 0, DateTimeKind.Utc),
            UserId = new Guid("*************-2222-2222-************")
        });
    }

    // protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) =>
    //     optionsBuilder.UseNpgsql(_connectionString);

    public override int SaveChanges()
    {
        UpdateTimeStamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimeStamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimeStamps()
    {
        var entries = ChangeTracker
            .Entries<ITrackable>()
            .Where(e => 
                e.State is EntityState.Modified or EntityState.Added);

        foreach (var entry in entries)
        {
            //Wtf?
            // if (entry.State is EntityState.Added)
            // {
            //     entry.Entity.CreatedAt = DateTime.UtcNow;
            // }

            entry.Entity.UpdatedAt = DateTime.UtcNow;
        }
    }
}