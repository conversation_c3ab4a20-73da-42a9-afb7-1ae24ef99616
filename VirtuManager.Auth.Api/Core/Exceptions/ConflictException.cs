namespace VirtuManager.Auth.Api.Core.Exceptions;

public class ConflictException : Exception
{
    public ConflictException()
    {
    }

    public ConflictException(string name) : base($"\"{name}\" already exists.")
    {
    }
    
    public ConflictException(string name, object key) : base($"\"{name}\" with {key} already exists.")
    {
    }
    
    public ConflictException(string message, Exception innerException)
        : base(message, innerException)
    {
    }
}