<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>VirtuManager.Auth.Api</RootNamespace>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3"/>
        <PackageReference Include="DotNetEnv" Version="3.1.1"/>
        <PackageReference Include="Elastic.Serilog.Sinks" Version="8.12.3" />
        <PackageReference Include="Grpc.AspNetCore" Version="2.70.0"/>
        <PackageReference Include="Grpc.Tools" Version="2.71.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.11">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11"/>
        <PackageReference Include="Serilog" Version="4.2.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0"/>
    </ItemGroup>

    <ItemGroup>
        <Protobuf Include="Protos\user.proto" GrpcServices="Server"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\VirtuManager.Auth.Infrastructure\VirtuManager.Auth.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Core\Exceptions\" />
      <Folder Include="Database\" />
      <Folder Include="Repositories\Interfaces\" />
    </ItemGroup>

</Project>
