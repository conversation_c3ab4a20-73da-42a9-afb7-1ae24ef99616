using virtu_manager_auth.Api.Presets.Paged;
using virtu_manager_auth.Api.ViewModels;
using virtu_manager_auth.Extensions;
using virtu_manager_auth.Mapping;
using virtu_manager_auth.Repositories;
using VirtuManager.Auth.Api.Services.Interfaces;
using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Domain.Exceptions;

namespace virtu_manager_auth.Services;

public class GetUsersService : IGetUsersService
{
    private readonly UsersRepository _usersRepository;
    private readonly ILogger<IGetUsersService> _logger;
    
    public GetUsersService(UsersRepository usersRepository, ILogger<IGetUsersService> logger)
    {
        _usersRepository = usersRepository;
        _logger = logger;
    }

    public async Task<IEnumerable<UserViewModel>> GetAll(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting all users");
        var users = await _usersRepository.GetAll();

        _logger.LogInformation("Successfully got {Count} users",
            users.Count);

        return users.Select(u => u.ToViewModel()).ToList();
    }

    public async Task<UserViewModel> GetById(Guid id, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user with '{Id}'", id);

        var user = await _usersRepository.GetUserById(id);

        if (user is null)
        {
            _logger.LogError("User with id '{Id}' was not found", id);
            throw new NotFoundException(nameof(User), id);
        }
        
        _logger.LogInformation("Successfully got user with id '{Id}'", id);

        return user.ToViewModel();
    }

    public async Task<UserViewModel> GetByLogin(string login, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user with '{Login}'", login);
        var user = await _usersRepository.GetUserByLogin(login);
        
        if (user is null)
        {
            _logger.LogError("User with login '{Login}' was not found", login);
            throw new NotFoundException(nameof(User), login);
        }

        _logger.LogInformation("Successfully got user with login '{Login}'", login);        
        return user.ToViewModel();
    }

    public async Task<PaginationResponse<UserViewModel>> GetPaged(int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting users page: page {PageNumber}, size: {PageSize}", pageNumber, pageSize);
        
        var (items, totalCount) = await _usersRepository.GetUsersPaged(pageNumber, pageSize);
        
        var users = items!.Select(u => u.ToViewModel()).ToList();

        _logger.LogInformation("Successfully got {Count} users on page {PageNumber}",
            users.Count, pageNumber);

        return new PaginationResponse<UserViewModel>
        {
            Items = users,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalCount = totalCount,
            TotalPages = totalCount.CalculateTotalPages(pageSize)
        };
    }
}