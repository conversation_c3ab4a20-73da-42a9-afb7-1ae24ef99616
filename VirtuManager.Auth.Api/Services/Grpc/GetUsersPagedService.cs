using Grpc.Core;
using Microsoft.AspNetCore.Authorization;
using virtu_manager_auth_service;
using virtu_manager_auth.Mapping;
using VirtuManager.Auth.Api.Services.Interfaces;

namespace virtu_manager_auth.Services.Grpc;

[Authorize]
public class GetUsersPagedService : GrpcUserService.GrpcUserServiceBase
{
    private readonly IGetUsersService _getUsersService;

    public GetUsersPagedService(IGetUsersService getUsersService)
    {
        _getUsersService = getUsersService ?? throw new ArgumentNullException(nameof(getUsersService));
    }

    public override async Task<GetUsersPagedResponse> GetUsersPaged(GetUsersPagedRequest request, ServerCallContext context)
    {
        var result = await _getUsersService.GetPaged(request.PageNumber, request.PageSize);

        var response = new GetUsersPagedResponse
        {
            TotalCount = result.TotalCount,
            PageNumber = result.PageNumber,
            PageSize = result.PageSize,
            TotalPages = result.TotalPages,
        };
        
        response.Users.AddRange(result.Items!.Select(u => u.ToGrpcModel()));
        
        return response;
    }
}