using virtu_manager_auth.Services.Interfaces;
using VirtuManager.Auth.Api.Services.Interfaces;
using VirtuManager.Auth.Domain.Contracts;

namespace VirtuManager.Auth.Api.Services;

public class HashDataService : IHashService<BcryptAlgorithm>
{
    private readonly int _workFactor;
    
    public HashDataService(int workFactor)
    {
        ArgumentNullException.ThrowIfNull(workFactor);
        
        if (workFactor <=  0) throw new ArgumentException("Work factor must be greater than 0");

        _workFactor = workFactor;
    }
    
    public string HashData(string data)
    {
        ArgumentNullException.ThrowIfNull(data);
        
        return BCrypt.Net.BCrypt.HashPassword(data, _workFactor);
    }

    public bool VerifyData(string data, string hash)
    {
        ArgumentNullException.ThrowIfNull(data);
        ArgumentNullException.ThrowIfNull(hash);
        
        return BCrypt.Net.BCrypt.Verify(data, hash);
    }
}