// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Protos/user.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace virtu_manager_auth_service {
  public static partial class GrpcUserService
  {
    static readonly string __ServiceName = "User.GrpcUserService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::virtu_manager_auth_service.GetUserByGuidRequest> __Marshaller_User_GetUserByGuidRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::virtu_manager_auth_service.GetUserByGuidRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::virtu_manager_auth_service.User> __Marshaller_User_User = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::virtu_manager_auth_service.User.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::virtu_manager_auth_service.GetUserByLoginRequest> __Marshaller_User_GetUserByLoginRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::virtu_manager_auth_service.GetUserByLoginRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::virtu_manager_auth_service.GetUsersPagedRequest> __Marshaller_User_GetUsersPagedRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::virtu_manager_auth_service.GetUsersPagedRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::virtu_manager_auth_service.GetUsersPagedResponse> __Marshaller_User_GetUsersPagedResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::virtu_manager_auth_service.GetUsersPagedResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Google.Protobuf.WellKnownTypes.Empty> __Marshaller_google_protobuf_Empty = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Google.Protobuf.WellKnownTypes.Empty.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::virtu_manager_auth_service.GetUsersResponse> __Marshaller_User_GetUsersResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::virtu_manager_auth_service.GetUsersResponse.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::virtu_manager_auth_service.GetUserByGuidRequest, global::virtu_manager_auth_service.User> __Method_GetUserByGuid = new grpc::Method<global::virtu_manager_auth_service.GetUserByGuidRequest, global::virtu_manager_auth_service.User>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserByGuid",
        __Marshaller_User_GetUserByGuidRequest,
        __Marshaller_User_User);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::virtu_manager_auth_service.GetUserByLoginRequest, global::virtu_manager_auth_service.User> __Method_GetUserByLogin = new grpc::Method<global::virtu_manager_auth_service.GetUserByLoginRequest, global::virtu_manager_auth_service.User>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserByLogin",
        __Marshaller_User_GetUserByLoginRequest,
        __Marshaller_User_User);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::virtu_manager_auth_service.GetUsersPagedRequest, global::virtu_manager_auth_service.GetUsersPagedResponse> __Method_GetUsersPaged = new grpc::Method<global::virtu_manager_auth_service.GetUsersPagedRequest, global::virtu_manager_auth_service.GetUsersPagedResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUsersPaged",
        __Marshaller_User_GetUsersPagedRequest,
        __Marshaller_User_GetUsersPagedResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Google.Protobuf.WellKnownTypes.Empty, global::virtu_manager_auth_service.GetUsersResponse> __Method_GetAllUsers = new grpc::Method<global::Google.Protobuf.WellKnownTypes.Empty, global::virtu_manager_auth_service.GetUsersResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetAllUsers",
        __Marshaller_google_protobuf_Empty,
        __Marshaller_User_GetUsersResponse);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::virtu_manager_auth_service.UserReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of GrpcUserService</summary>
    [grpc::BindServiceMethod(typeof(GrpcUserService), "BindService")]
    public abstract partial class GrpcUserServiceBase
    {
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::virtu_manager_auth_service.User> GetUserByGuid(global::virtu_manager_auth_service.GetUserByGuidRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::virtu_manager_auth_service.User> GetUserByLogin(global::virtu_manager_auth_service.GetUserByLoginRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::virtu_manager_auth_service.GetUsersPagedResponse> GetUsersPaged(global::virtu_manager_auth_service.GetUsersPagedRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::virtu_manager_auth_service.GetUsersResponse> GetAllUsers(global::Google.Protobuf.WellKnownTypes.Empty request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(GrpcUserServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetUserByGuid, serviceImpl.GetUserByGuid)
          .AddMethod(__Method_GetUserByLogin, serviceImpl.GetUserByLogin)
          .AddMethod(__Method_GetUsersPaged, serviceImpl.GetUsersPaged)
          .AddMethod(__Method_GetAllUsers, serviceImpl.GetAllUsers).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, GrpcUserServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetUserByGuid, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::virtu_manager_auth_service.GetUserByGuidRequest, global::virtu_manager_auth_service.User>(serviceImpl.GetUserByGuid));
      serviceBinder.AddMethod(__Method_GetUserByLogin, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::virtu_manager_auth_service.GetUserByLoginRequest, global::virtu_manager_auth_service.User>(serviceImpl.GetUserByLogin));
      serviceBinder.AddMethod(__Method_GetUsersPaged, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::virtu_manager_auth_service.GetUsersPagedRequest, global::virtu_manager_auth_service.GetUsersPagedResponse>(serviceImpl.GetUsersPaged));
      serviceBinder.AddMethod(__Method_GetAllUsers, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Google.Protobuf.WellKnownTypes.Empty, global::virtu_manager_auth_service.GetUsersResponse>(serviceImpl.GetAllUsers));
    }

  }
}
#endregion
