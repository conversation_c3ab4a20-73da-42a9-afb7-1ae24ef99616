using Microsoft.EntityFrameworkCore;
using virtu_manager_auth.Auth;
using virtu_manager_auth.Auth.Interfaces;
using virtu_manager_auth.Repositories;
using virtu_manager_auth.Services;
using virtu_manager_auth.Utils;
using virtu_manager_auth.Utils.Interfaces;
using VirtuManager.Auth.Api.Auth;
using VirtuManager.Auth.Api.Extensions.Di;
using VirtuManager.Auth.Api.Services;
using VirtuManager.Auth.Api.Services.Interfaces;
using VirtuManager.Auth.Api.Utils;
using VirtuManager.Auth.Domain.Contracts;
using VirtuManager.Auth.Domain.Services;
using VirtuManager.Auth.Infrastructure.Database;
using VirtuManager.Auth.Infrastructure.Services;

var builder = WebApplication.CreateBuilder(args);

builder.Host.UseElasticsearchSerilog();

var configuration = builder.Configuration;

builder.Services.AddTransient<IHashDataService<BcryptAlgorithm>>(_ => new BcryptHashDataService(12));

builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseNpgsql(configuration.GetConnectionString("DefaultConnection"));
});

builder.Services.AddControllers();

builder.Services.AddJwtAuthentication(configuration);

builder.Services.AddTransient<IJwtService, JwtService>();
builder.Services.AddTransient<ICookieUtils, CookieUtils>();

builder.Services.AddTransient<IGetUsersService, GetUsersService>();

builder.Services.AddTransient<UsersRepository>();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

app.MapGet("/health", () => "OK");

// Create database on start project
using var scope = app.Services.CreateScope();
var db = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

await db.Database.EnsureDeletedAsync();
await db.Database.EnsureCreatedAsync();


// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors(c =>
{
    c.AllowAnyMethod();
    c.AllowAnyHeader();
    c.SetIsOriginAllowed(origin => true);
    c.AllowCredentials();
});

app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

// Засосать порт из .env, потом мб в Dockerfile настрою
// var port = DotNetEnv.Env.GetInt("APPLICATION_PORT", 5000);
// var urls = $"http://localhost:{port}";
// app.Urls.Add(urls);

app.Run();