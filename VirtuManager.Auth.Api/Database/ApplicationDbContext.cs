using Microsoft.EntityFrameworkCore;
using virtu_manager_auth.Services.Interfaces;
using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Domain.Enums;

namespace virtu_manager_auth.Database;

public class ApplicationDbContext : DbContext
{
    public DbSet<User> Users { get; private set; }  

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options) {}
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        /*
         * Password = admin123
         * Hash Type = Bcrypt
         * Rounds = 12
         */
        modelBuilder.Entity<User>().HasData(new User
        {
            Id = new Guid("11111111-1111-1111-1111-111111111111"),
            Login = "admin",
            PasswordHash = "$2a$12$gvqA81adecX8aYe/xVfWU.oZjCGdMhZ25e2oTg7F1KPr19h28UuKa",
            Role = UserRole.Admin,
            Email = "<EMAIL>",
            CreatedAt = new DateTime(2025, 4, 9, 0, 0, 0, DateTimeKind.Utc)
        });

        /*
         * Password = developer123
         * Hash Type = Bcrypt
         * Rounds = 12
         */
        modelBuilder.Entity<User>().HasData(new User
        {
            Id = new Guid("*************-2222-2222-************"),
            Login = "developer",
            PasswordHash = "$2a$12$yqumTiB4BdK6OU3i0g2DrOrorotgx5YcdkcP1wRrQDb2LnJaAlfgy",
            Role = UserRole.Developer,
            Email = "<EMAIL>",
            CreatedAt = new DateTime(2025, 4, 9, 0, 0, 0, DateTimeKind.Utc)
        });

        modelBuilder.Entity<User>().HasKey(k => k.Id);
    }
}